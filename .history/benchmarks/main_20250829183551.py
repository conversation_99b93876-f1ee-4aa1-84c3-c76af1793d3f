import os
import time
from api.api import CodeBaseDevApi
from treo.treo_benchmark import TreoCodebaseBenchmarkModel

def main():
    output_dir = "./results/treo/codebase_dev"
    time_str = time.strftime("%Y%m%d%H%M%S", time.localtime())
    output_dir = os.path.join(output_dir, time_str)

    for question_key in ["question"]:
        base_api = CodeBaseDevApi({"api_url": "http://localhost:3451/api/v1", "timeout": 300})
        treo_codebase_benchmark_model = TreoCodebaseBenchmarkModel(
            base_api, 
            qas_file="/Users/<USER>/01-Projects/treo/verified/qas_lite.json", 
            output_dir=output_dir
        )
        treo_codebase_benchmark_model.init_dataset()
        treo_codebase_benchmark_model.evaluate(question_key=question_key, max_workers=10, search_tool="any")


if __name__ == "__main__":
    main()
