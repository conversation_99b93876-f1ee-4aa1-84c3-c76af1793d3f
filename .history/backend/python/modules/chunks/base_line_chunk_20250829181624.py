from typing import List, Tuple
from modules.common.schema import Chunk
from modules.chunks.IChunk import IChunk


class BaseLineChunk(IChunk):
    def chunk_file(self, file_path: str, file_content: str, window_size: int = 50, overflow_size: int = 10) -> Tuple[str, List[Chunk]]:
        # 移除代码中的空白行
        lines = file_content.splitlines()
        key_structure = ""
        chunks = []

        # 处理空文件的情况
        if not lines:
            # 对于空文件，创建一个空的代码块
            chunks.append(
                Chunk(
                    file_path=file_path,
                    start_line=0,
                    end_line=0,
                    content="",
                )
            )
            return key_structure, chunks

        # 确保步长至少为1，避免无限循环
        step = max(1, window_size - overflow_size)

        # 按照window_size和overflow_size进行分块
        for i in range(0, len(lines), step):
            # 修复：确保 start_line 不超出文件范围
            start_line = i

            # 修复：确保 end_line 不超出文件范围，并且是包含的（0-based）
            # 使用 max(0, len(lines) - 1) 确保即使是单行文件也不会出现负数
            end_line = min(i + window_size - 1, max(0, len(lines) - 1))

            # 构建 key_structure（修复：检查索引范围）
            if i < len(lines):
                key_structure += f"L:{i} " + lines[i] + "\n...\n"

            # 提取内容（修复：使用正确的范围）
            chunk_content = "\n".join(lines[start_line:end_line + 1])

            chunks.append(
                Chunk(
                    file_path=file_path,
                    start_line=start_line,
                    end_line=end_line,
                    content=chunk_content,
                )
            )

            if end_line == len(lines) - 1:
                break

        return key_structure, chunks
    