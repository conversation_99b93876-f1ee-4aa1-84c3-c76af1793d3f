import datetime
from typing import List, Any
from modules.llm.llm_client import LL<PERSON>lient, default_llm_client
from utils.file import FileNode, build_file_tree, calculate_repo_size
from utils.trace_logger import get_trace_logger
from core.config import get_config, set_config
from modules.common.constants import File<PERSON>ilterMode, IOToolEnum, SearchToolEnum
from modules.integrations.tools.search.any_search import get_search_tool_instance
from modules.integrations.tools.io.any_io import AnyIOTool
from modules.common.schema import CodeSnippet, ContextOperation, ContextOperationResult, SearchResult, SearchQuery
from modules.searchrouter.prompts import GENERATE_NEW_QUERY_PROMPT, READ_CONTEXT_PROMPT, SNIPPETS_REORDER_PROMPT, SYSTEM_PROMPTS
from modules.integrations.database.sqlite.client import get_sqlite_client

trace_logger = get_trace_logger(__name__)

class SearchRouter:
    def __init__(
        self,
        repo_path: str,
        repo_info: str = "",
        llm_client: LLMClient = None,
        search_tool: SearchToolEnum = SearchToolEnum.ANY
    ):
        self.side_memory: List[Any]  = [] # 旁路记忆，可以用于暂存从IDE中获取的信息

        self.repo_info = repo_info # 仓库信息
        self.repo_path = repo_path # 搜索目标路径
        
        self.search_tool = search_tool # 搜索工具类型
        
        self._prepare_repo_data(repo_path)

        self.search_instance = get_search_tool_instance(
            SearchToolEnum.ANY, # 使用ANY类型的Search工具来中转对其他工具的调用，避免直接初始化对应的search工具，通过这种方式统一工具调用的输入入口
            repo_path, 
            enabled_search_tools=[search_tool.value] if search_tool != SearchToolEnum.ANY else get_config().deepsearch.enabled_search_tools) # 可使用的搜索工具，默认是ANY，此时按照配置文件中的设置来确定可用的搜索工具
        self.io_instance = AnyIOTool(repo_path=repo_path)

        self.llm_client = llm_client or default_llm_client

        self.config = get_config().deepsearch

    async def search(self, query: str) -> SearchResult:
        result = SearchResult(original_query=query)
        original_query = query

        # 1. 读取所需要的信息，循环MAX_READ_ITERATION次（default=1）
        # 获取当前仓库树结构
        repo_node = FileNode(path=self.repo_path, 
                             name=self.repo_path.split("/")[-1], 
                             type="directory", 
                             children=build_file_tree(root_dir=self.repo_path, start_dir=self.repo_path, max_leaf_nodes=300, filter_mode=FileFilterMode.LOCAL))
        
        sub_queries = await self._split_queris(query, repo_node)
        
        for sub_query in sub_queries:
            self.side_memory.extend(
                self._execute_read_context_operations(sub_query.context_operations)
            )
        all_queries = [sub_query for sub_query in sub_queries]
        code_snippets = []

        # 2. 拆分查询方向，并选定搜索工具
        for iteration in range(self.config.max_iterations):
            if not sub_queries:
                trace_logger.info("未生成新查询，搜索结束")
                break
            
            # 生成子查询
            new_queries = await self._generate_new_queries(original_query, all_queries)
            all_queries.extend(new_queries)

            # 3. 并发搜索
            try:
                search_operations = []
                for new_query in new_queries:
                    search_operations.extend(new_query.context_operations)
                new_snippets = await self._search_and_filter(context_operations=search_operations)
            except Exception as e:
                trace_logger.error(f"Iteration {iteration + 1}: Search Failed: {e}")
                break

            if not new_snippets:
                # DeepSearch的方法是预设首先拆分的搜索方向没有错误，如果子查询没有找到相关代码，说明当前搜索方向已经穷尽，可以结束搜索
                trace_logger.warning(f"Iteration {iteration + 1}: Not Found Any Code Snippets")
                break
            
            code_snippets.extend(new_snippets)
            code_snippets = self._merge_snippets(code_snippets)

            result.iterations = iteration + 1

        # 4. 将side_memory中读取过的文件信息加入到code_snippets中参与排序
        for element in self.side_memory:
            if isinstance(element, ContextOperationResult) and element.operation.tool == IOToolEnum.FILE:
                code_snippets.append(
                    CodeSnippet(
                        file_path=element.operation.context_uri,
                        start_line=0,
                        end_line=max(0, len(element.result.splitlines()) - 1),
                        content=element.result, # fix: 这里应该会有个bug，这里读取出来的content应该是文件结构，和一般的CodeSnippet合并时可能会发生行不匹配导致合并错乱
                        context_before="",
                        context_after="",
                        score=0.0
                    )
                )

        # 5. 代码片段去重
        code_snippets = self._merge_snippets(code_snippets)
        code_snippets = self._merge_file_snippets(code_snippets)

        # 6. 重排序
        code_snippets = await self._reorder_snippets(original_query, code_snippets, all_queries)
        result.code_snippets = code_snippets

        return result
    
    async def search_stream(self, query: str):
        """
        流式搜索，参考DeepSearch的数据返回格式

        Args:
            query: 用户查询

        Yields:
            str: Server-Sent Events格式的数据流
        """
        import json
        import time
        import asyncio

        try:
            # 开始搜索
            trace_logger.info(f"Start SearchRouter Stream for Query: {query}")
            yield f'data: {json.dumps({"type": "start", "message": "开始智能搜索", "timestamp": time.time()})}\n\n'
            await asyncio.sleep(0.01)

            # 1. 构建仓库文件树
            yield f'data: {json.dumps({"type": "process", "message": "分析仓库结构...", "timestamp": time.time()})}\n\n'
            repo_node = FileNode(
                path=self.repo_path,
                name=self.repo_path.split("/")[-1],
                type="directory",
                children=build_file_tree(
                    root_dir=self.repo_path,
                    start_dir=self.repo_path,
                    max_leaf_nodes=300,
                    filter_mode=FileFilterMode.LOCAL
                )
            )
            await asyncio.sleep(0.01)

            # 2. 查询分解
            yield f'data: {json.dumps({"type": "process", "message": "分解查询意图...", "timestamp": time.time()})}\n\n'
            try:
                sub_queries = await self._split_queris(query, repo_node)
                yield f'data: {json.dumps({"type": "process", "message": f"生成 {len(sub_queries)} 个子查询", "timestamp": time.time()})}\n\n'
            except Exception as e:
                yield f'data: {json.dumps({"type": "error", "message": f"查询分解失败: {e}", "timestamp": time.time()})}\n\n'
                return
            await asyncio.sleep(0.01)

            # 3. 执行上下文操作
            yield f'data: {json.dumps({"type": "process", "message": "收集上下文信息...", "timestamp": time.time()})}\n\n'
            for sub_query in sub_queries:
                context_results = self._execute_read_context_operations(sub_query.context_operations)
                self.side_memory.extend(context_results)
            await asyncio.sleep(0.01)

            all_queries = [sub_query for sub_query in sub_queries]
            code_snippets = []

            # 4. 迭代搜索
            for iteration in range(self.config.max_iterations):
                if not sub_queries:
                    yield f'data: {json.dumps({"type": "complete", "message": "未生成新查询，搜索结束", "timestamp": time.time()})}\n\n'
                    break

                yield f'data: {json.dumps({"type": "process", "message": f"开始第 {iteration + 1} 轮搜索", "timestamp": time.time()})}\n\n'

                # 生成新查询
                try:
                    new_queries = await self._generate_new_queries(query, all_queries)
                    all_queries.extend(new_queries)
                    yield f'data: {json.dumps({"type": "process", "message": f"生成 {len(new_queries)} 个新查询", "timestamp": time.time()})}\n\n'
                except Exception as e:
                    yield f'data: {json.dumps({"type": "error", "message": f"生成新查询失败: {e}", "timestamp": time.time()})}\n\n'
                    break
                await asyncio.sleep(0.01)

                # 并发搜索
                yield f'data: {json.dumps({"type": "process", "message": "执行并发搜索...", "timestamp": time.time()})}\n\n'
                try:
                    search_operations = []
                    for new_query in new_queries:
                        search_operations.extend(new_query.context_operations)

                    new_snippets = await self._search_and_filter(context_operations=search_operations)
                    yield f'data: {json.dumps({"type": "process", "message": f"找到 {len(new_snippets)} 个代码片段", "timestamp": time.time()})}\n\n'
                except Exception as e:
                    yield f'data: {json.dumps({"type": "error", "message": f"搜索失败: {e}", "timestamp": time.time()})}\n\n'
                    break

                if not new_snippets:
                    yield f'data: {json.dumps({"type": "complete", "message": f"第 {iteration + 1} 轮搜索未找到相关代码，搜索结束", "timestamp": time.time()})}\n\n'
                    break

                code_snippets.extend(new_snippets)
                code_snippets = self._merge_snippets(code_snippets)
                await asyncio.sleep(0.01)

            # 5. 结果优化
            yield f'data: {json.dumps({"type": "process", "message": "优化搜索结果...", "timestamp": time.time()})}\n\n'
            code_snippets = self._merge_snippets(code_snippets)
            code_snippets = self._merge_file_snippets(code_snippets)
            await asyncio.sleep(0.01)

            # 6. 重排序
            yield f'data: {json.dumps({"type": "process", "message": "重新排序结果...", "timestamp": time.time()})}\n\n'
            try:
                code_snippets = await self._reorder_snippets(query, code_snippets, all_queries)
            except Exception as e:
                trace_logger.warning(f"重排序失败: {e}")
                yield f'data: {json.dumps({"type": "process", "message": f"重排序失败，使用默认排序: {e}", "timestamp": time.time()})}\n\n'
            await asyncio.sleep(0.01)

            # 7. 输出结果摘要
            if code_snippets:
                # 按文件分组显示结果
                file_groups = {}
                for snippet in code_snippets:
                    if snippet.file_path not in file_groups:
                        file_groups[snippet.file_path] = []
                    file_groups[snippet.file_path].append(snippet)

                yield f'data: {json.dumps({"type": "process", "message": f"📊 搜索结果: {len(code_snippets)} 个代码片段，{len(file_groups)} 个文件", "timestamp": time.time()})}\n\n'

                for file_path, snippets in file_groups.items():
                    yield f'data: {json.dumps({"type": "process", "message": f"📁 {file_path} ({len(snippets)} 个片段)", "timestamp": time.time()})}\n\n'
                    await asyncio.sleep(0.01)
            else:
                yield f'data: {json.dumps({"type": "process", "message": "⚠️ 未找到相关代码片段", "timestamp": time.time()})}\n\n'

            # 8. 完成搜索
            trace_logger.info(f"SearchRouter Stream Completed: {len(code_snippets)} snippets found")
            yield f'data: {json.dumps({"type": "process", "message": f"✅ 智能搜索完成: 共找到 {len(code_snippets)} 个相关代码片段", "timestamp": time.time()})}\n\n'

            # 发送完成事件，包含搜索结果数据
            completion_data = {
                "type": "complete",
                "message": "搜索完成",
                "timestamp": time.time(),
                "result": {
                    "original_query": query,
                    "code_snippets": [snippet.model_dump() for snippet in code_snippets],
                    "total_snippets": len(code_snippets),
                    "total_files": len(set(snippet.file_path for snippet in code_snippets))
                }
            }
            yield f'data: {json.dumps(completion_data)}\n\n'

        except Exception as e:
            trace_logger.error(f"SearchRouter Stream Error: {e}")
            yield f'data: {json.dumps({"type": "error", "message": f"搜索过程中发生错误: {e}", "timestamp": time.time()})}\n\n'

    async def _split_queris(self, query: str, repo_struct: str) -> List[SearchQuery]:
        prompt = READ_CONTEXT_PROMPT.format(
            query=query,
            repository_root=self.,
            repo_struct=repo_struct,
            max_subqueries=self.config.max_sub_queries,
            available_tools=self.io_instance.description,
            tools_examples=self.io_instance.examples
        )
        
        try:
            response_text = await self.llm_client.call_async(prompt, SYSTEM_PROMPTS['query_split'], stream=False)
        except Exception as e:
            trace_logger.error(f"Split Query Failed: {e}")
            return []

        return SearchQuery.parse_response_text(response_text)[:self.config.max_sub_queries]

    def _execute_read_context_operations(self, context_operations: List[ContextOperation]) -> List[ContextOperationResult]:
        if not context_operations:
            return []
        
        operation_results = []
        for operation in context_operations:
            operation_result = self.io_instance.read(query=operation.xml_content, io_tool=operation.tool)
            operation_results.append(ContextOperationResult(operation=operation, result=operation_result))
            
        return operation_results
    
    async def _generate_new_queries(self, original_query: str, all_quires: List[SearchQuery]) -> List[SearchQuery]:
        prompt = GENERATE_NEW_QUERY_PROMPT.format(
            question=original_query,
            context_content="\n----------\n".join([str(element) for element in self.side_memory]),
            max_new_queries=self.config.max_new_queries,
            tool_description=self.search_instance.description,
            tool_exampls=self.search_instance.examples,
            previous_queries="\n\t- ".join([query.text for query in all_quires])
        )

        respose_text = await self.llm_client.call_async(prompt, SYSTEM_PROMPTS['generate_new_query'], stream=False)

        structure_results = SearchQuery.parse_response_text(respose_text)

        return structure_results[:self.config.max_new_queries]
    

    async def _search_and_filter(self, context_operations: List[ContextOperation]) -> List[str]:
        """
        异步搜索并过滤代码片段

        Args:
            queries: 查询列表
            search_tools: 搜索工具列表

        Returns:
            List[CodeSnippet]: 过滤后的代码片段
        """
        # 并行检索所有子查询
        trace_logger.info(f"Async Searching Tool: {context_operations}")
    
        if not context_operations:
            return []

        # 使用asyncio.gather进行并发搜索
        search_tasks = []
        for operation in context_operations:
            # 使用异步搜索方法
            task = self.search_instance.search_async(query=operation.xml_content, search_tool=operation.tool)
            search_tasks.append(task)

        # 等待所有搜索任务完成
        search_results = []
        for task in search_tasks:
            try:
                snippets = await task
                trace_logger.info(f"Found {len(snippets)} Code Snippets")
                search_results.extend(snippets)
            except Exception as exc:
                trace_logger.info(f"Search Failed: {exc}")
                search_results.extend([])

        # 统计总的代码片段数量
        trace_logger.info(f"Async Found {len(search_tasks)} Code Snippets, Start Filtering...")

        return search_results

    def _merge_snippets(self, snippets: List[CodeSnippet]) -> List[CodeSnippet]:
        """
        去重代码片段（基于文件路径和行号）
        
        Args:
            snippets: 代码片段列表
            
        Returns:
            List[CodeSnippet]: 去重后的代码片段列表
        """
        seen_stack = []
        unique_snippets = []
        
        sorted_snippets = sorted(snippets, key=lambda x: (x.file_path, x.start_line, x.end_line))

        for snippet in sorted_snippets:
            # 使用文件路径和行号作为唯一标识
            key = (snippet.file_path, snippet.start_line, snippet.end_line)
            
            if seen_stack and seen_stack[-1][0] == key[0] and seen_stack[-1][1] <= key[1] <= seen_stack[-1][2]:
                # 更新seen_stack的end_line
                seen_stack[-1][2] = max(seen_stack[-1][2], key[2])
                
                # 如果end_line在前一个snippet后面，则合并snippet内容
                if unique_snippets[-1].end_line >= key[2]:
                    unique_snippets[-1].content = "\n".join(unique_snippets[-1].content.splitlines()[:snippet.start_line - snippet.start_line]) + "\n" + snippet.content
                    unique_snippets[-1].score += snippet.score # 合并后的score相加

                # 更新end_line
                unique_snippets[-1].end_line = seen_stack[-1][2]
                    
            else:   
                seen_stack.append(list(key)) # 因为元素可能被修改，tuple是不可变元组，因此使用list存入
                unique_snippets.append(snippet)

        return unique_snippets
    
    def _merge_file_snippets(self, snippets: List[CodeSnippet]) -> List[CodeSnippet]:
        """
        按照文件路径合并snippets，如果文件路径相同，则合并为一个snippet
        如果相同文件的snippets之间的行号不连续，补充......指代中间的省略部分

        Args:
            snippets: 代码片段列表

        Returns:
            List[CodeSnippet]: 合并后的代码片段列表
        """
        if not snippets:
            return []

        unique_snippets = self._merge_snippets(snippets)

        file_groups = {}
        for snippet in unique_snippets:
            if snippet.file_path not in file_groups:
                file_groups[snippet.file_path] = []
            file_groups[snippet.file_path].append(snippet)

        merged_snippets = []

        for file_path, file_snippets in file_groups.items():
            # 按起始行号排序
            file_snippets.sort(key=lambda x: x.start_line)

            # 然后合并同一文件的所有片段，处理不连续的情况
            if len(file_snippets) == 1:
                merged_snippets.append(file_snippets[0])
            else:
                # 合并为一个大的片段，中间用省略号连接
                merged_content = "\n...\n".join(snippet.content for snippet in file_snippets)
                merged_startline = file_snippets[0].start_line
                merged_endline = file_snippets[-1].end_line
                
                merged_snippets.append(CodeSnippet(
                    file_path=file_path,
                    start_line=merged_startline,
                    end_line=merged_endline,
                    content=merged_content,
                    context_before="",
                    context_after="",
                    score=sum(snippet.score for snippet in file_snippets)
                ))

        return merged_snippets
    
    async def _reorder_snippets(self, original_query: str, snippets: List[CodeSnippet], all_quires: List[SearchQuery]) -> List[CodeSnippet]:
        """
        重排序代码片段

        Args:
            snippets: 代码片段列表
            original_query: 原始查询
            all_quires: 所有查询列表
            side_memories: 侧边记忆列表

        Returns:
            List[CodeSnippet]: 重排序后的代码片段列表
        """
        if not snippets:
            return []

        combined_query = original_query + "\n\t- " + "\n\t- ".join([query.text for query in all_quires])

        # 构建带索引的代码片段摘要
        code_summary = self._build_file_index_summary(snippets)

        prompt = SNIPPETS_REORDER_PROMPT.format(
            max_code_snippets=20,
            query=combined_query,
            code_snippets=code_summary
        )

        response_text = await self.llm_client.call_async(prompt, SYSTEM_PROMPTS['reorder'], stream=False)
        trace_logger.debug(f"Async Reorder Response: {response_text}")

        # 解析LLM返回的JSON格式结果
        try:
            # 提取JSON内容
            import json

            # 尝试直接解析JSON
            try:
                score_dict = json.loads(response_text.strip().replace("\n", ""))
            except json.JSONDecodeError:
                trace_logger.warning("无法从LLM响应中提取JSON格式的评分结果")
                return snippets  # 返回原始顺序

            

            # 创建评分映射 (将字符串键转换为整数)

            for index, snippet in enumerate(snippets):
                if str(index) in score_dict:
                    snippets[index].score = score_dict[str(index)]
                else:
                    snippets[index].score = 0.0

            return sorted(snippets, key=lambda x: x.score, reverse=True)[:20]

        except Exception as e:
            trace_logger.error(f"解析异步重排序结果失败: {e}")
            # 如果解析失败，返回原始片段（限制数量）
            return snippets
    
    def _build_file_index_summary(self, snippets: List[CodeSnippet], snippet_chars: int = 500) -> str:
        """
        构建文件索引摘要
        
        Args:
            snippets: 代码片段列表
            
        Returns:
            str: 文件索引摘要
        """
        if not snippets:
            return "暂无相关代码片段"
        
        summary = ""
        for file_index, snippet in enumerate(snippets):
            summary += f"{file_index}. {snippet.file_path}: {snippet.start_line}-{snippet.end_line}\n"
            summary += f"  {snippet.content[:snippet_chars]}\n"
            summary += f"--------------------------------------------\n\n"

        return summary

    def _prepare_repo_data(self, repo_path: str) -> None:
        sqlite_client = get_sqlite_client()
        repo = sqlite_client.get_repo(repo_path)
        if repo is None:
            trace_logger.info(f"Repo {repo_path} not found, creating...")
            repo_size = calculate_repo_size(repo_path)
            if repo_size > 50 * 1024 * 1024: # 50MB
                trace_logger.info(f"Repo {repo_path} size {repo_size} is too large, skiping...")
                # 修改配置，只启用grep搜索
                self.config.enabled_search_tools = [SearchToolEnum.GREP.value] 
                set_config(self.config)
                return
            else:
                trace_logger.info(f"Repo {repo_path} size {repo_size} is acceptable, preparing...")
                sqlite_client.create_repo(repo_path=repo_path, updated_at=int(datetime.datetime.now().timestamp()))
