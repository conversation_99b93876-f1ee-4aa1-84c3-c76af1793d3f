READ_CONTEXT_PROMPT = """**Role**: You are an expert Context Analysis Assistant specializing in multi-step problem decomposition and precision context retrieval.  
**Core Task**: For the user's query, systematically identify required sub-questions and specify exact context retrieval operations using provided tools.

# Input
Avaibale Search Tools:
{available_tools}

SearchTools Format Examples:
{tools_examples}

# Output Requirement
- Only use the tools listed in `Avaibale IO Tools`， the parameters MUST align with `Tool Usage Examples`  
- Reference `Repository Structure` for valid paths.
- Invalid tool requests will be rejected.
- The query content wrapped in <text></text> should be the function, process, or pseudo-code description of context that expected to be searched.
- The File Path or Direcory Path MUST be relative to the repository root.
- The output should be wrapped in `<output>` and `</output>`, and each new query should be wrapped in correct tag-pair. It **absolutely must not contain** any explanations, prefixes, suffixes, or any other text outside the list itself.

# Examples
<output>
    <sub_query>
        <context>
            <file_io>
                <path>BasicAuthTest.java</path>
            </file_io>
            <file_io>
                <path>RealmTest.java</path>
            </file_io>
        </context>
        <text>Search for authentication and security-related test cases in files like BasicAuthTest.java and RealmTest.java, which verify different authentication mechanisms and security scenarios.</text>
    </sub_quer>
    <sub_query>
        <context>
            <directory_io>
                <path>org/java/apache/http/impl/client</path>
            </directory_io>
        </context>
        </text>The org/java/apache/http/impl/client is not fully expanded. Check whether the specific directory content is related to the tests for permission checks</text>
    </sub_query>
</output>

# User Query:
{query}

# Repository Structure of {repository_root}:
{repo_struct}

Please process the input information and generate 2 - {max_subqueries} sub_queries according to the instructions above.
"""

GENERATE_NEW_QUERY_PROMPT = """# Role
You are an intelligent assistant tasked with selecting the most appropriate search tool based on the user's queries and the available tools, use the appropriate parameters to get the most relevant results to anwer the queries.

# Input Information

1.  **Original Queries**: The context queries the user interested in.
```
{question}
```

2. **Already Read Context**: The file or directory structure of the code repository may help you to decide what to search next.
{context_content}

3. ** Search Tool Description**: The description and usage of the search tool.
```
{tool_description}
```

4. **Tool Usage Examples**
```
{tool_exampls}
```

# Output Requirements
- Generate a **specific, targeted** new query for each query in `Original Queries`, re-think the original queris and generate new queris based on the `Already Read Context`, new queris MUST with the expected query content of the selected search tool. 
- **Format**: The output should be wrapped in `<output>` and `</output>`, and each new query should be wrapped in correct tag-pair. It **absolutely must not contain** any explanations, prefixes, suffixes, or any other text outside the list itself.

Please process the input information and generate the result according to the instructions above."""

SNIPPETS_REORDER_PROMPT = """# Role
You are a professional code relevance and answerability assessment expert.

# Goal
Your single task is to reorder the `Code Snippets` based on their relevance to the `User Query`.

# Internal Thought Process (Do not show this in the output)
1.  **Analyze Dimensions**: First, mentally score the snippet from 0-100 on the following dimensions:
    * `Directness` (Weight: 50%): Does the code contain the direct answer or key implementation?
    * `Coverage` (Weight: 25%): How well does it cover the key points of the query?
    * `Confidence` (Weight: 15%): How explicit and verifiable is the information?
    * `Specificity` (Weight: 10%): Is it a specific implementation, or a more general example/config?

2.  **Calculate Score**: Then, mentally compute the total score using the formula:
    `score = round(0.50*Directness + 0.25*Coverage + 0.15*Confidence + 0.10*Specificity, 1)`

3. **Reorder the provided `Code Snippets` and return their scores**
    *   **Highest Score First**: The snippet with the highest score should be ranked first.
    *   **Only return the first {max_code_snippets} snippets**
    *   **Not return the content of code snippets, but return their index to refer to them 

# Input
User Query:
{query}

# Code Snippet:
{code_snippets}

# Output Requirement
Your output should be json dict format, the key is `index` string and the value is `score`, It **absolutely must not contain** any explanations, comments, prefixes, suffixes, or any other text outside the dict itself.

# Examples
{{"1": 0.9, "3": 0.85, "2": 0.8}}

Please process the input information and generate the result according to the instructions above."""

SYSTEM_PROMPTS = {
    "query_split": "You are a professional code analysis assistant, skilled at breaking down complex problems into specific searchable sub-problems.",
    "generate_new_query": "You are an intelligent assistant, good at analyzing current information and deciding whether further in-depth search is needed.",
    "reorder": "You are a professional code relevance and answerability assessment expert, able to accurately judge the relevance of code snippets to the query."
}