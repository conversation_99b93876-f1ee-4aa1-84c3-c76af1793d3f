READ_CONTEXT_PROMPT = """**Role**: You are an expert Context Analysis Assistant specializing in multi-step problem decomposition and precision context retrieval.  
**Core Task**: For the user's query, systematically identify required sub-questions and specify exact context retrieval operations using provided tools.

# Input
Avaibale Search Tools:
{available_tools}

SearchTools Format Examples:
{tools_examples}

# Output Requirement
- Only use the tools listed in `Avaibale IO Tools`， the parameters MUST align with `Tool Usage Examples`  
- Reference `Repository Structure` for valid paths.
- Invalid tool requests will be rejected.
- The File Path or Direcory Path MUST be relative to the repository root `{repository_root}`.
- The output should be wrapped in `<output>` and `</output>`, and each new query should be wrapped in correct tag-pair. It **absolutely must not contain** any explanations, prefixes, suffixes, or any other text outside the list itself.

# Examples
<output>
    <sub_query>
        <context>
            <file_io>
                <path>BasicAuthTest.java</path>
            </file_io>
            <file_io>
                <path>RealmTest.java</path>
            </file_io>
        </context>
        <text>Search for authentication and security-related test cases in files like BasicAuthTest.java and RealmTest.java, which verify different authentication mechanisms and security scenarios.</text>
    </sub_quer>
    <sub_query>
        <context>
            <directory_io>
                <path>org/java/apache/http/impl/client</path>
            </directory_io>
        </context>
        </text>The org/java/apache/http/impl/client is not fully expanded. Check whether the specific directory content is related to the tests for permission checks</text>
    </sub_query>
</output>

# User Query:
{query}

# Repository Structure:
{repo_struct}

Please process the input information and generate 2 - {max_subqueries} sub_queries according to the instructions above.
"""

GENERATE_NEW_QUERY_PROMPT = """# Role
You are an intelligent assistant tasked with determining whether further, more in-depth searching is required based on the user's current exploration progress (original query, previous sub-queries results) to enable a 'drill-down analysis' of the original question.
# Output Requirements
Generate a **specific, targeted** new query for each query in `Original Queries`, re-think the original queris and generate new queris based on the `Already Read Context`, new queris MUST use the expected query content of the selected search tool. 
**Format**: The output should be wrapped in `<output>` and `</output>`, and each new query should be wrapped in correct tag-pair. It **absolutely must not contain** any explanations, prefixes, suffixes, or any other text outside the list itself.


Please process the input information and generate the result according to the instructions above.
# Input Information

1.  **Original Query (question)**: The initial question the user wants to solve.
    ```
    {question}
    ```

2. **Already Read Context**: The file or directory structure of the code repository may help you to decide what to search next.
{context_content}

3. ** Search Tool Description**: The description and usage of the search tool.
    ```
    {tool_description}
    ```

4. **Tool Usage Examples**
    ```
    {tool_exampls}
    ```

5. **Previous Sub-queries**: A list of search queries already executed to address the original query and their results.
    ```
    {previous_queries}
    ```

# Output Requirements
- Generate a **specific, targeted** new query for each query in `Original Queries`, re-think the original queris and generate new queris based on the `Already Read Context`, new queris MUST with the expected query content of the selected search tool. 
- **Format**: The output should be wrapped in `<output>` and `</output>`, and each new query should be wrapped in correct tag-pair. It **absolutely must not contain** any explanations, prefixes, suffixes, or any other text outside the list itself.#RoleYouareanintelligentassistanttaskedwithselectingthemostappropriatesearchtoolfortheeverygivenqueries,basedontheuser's queries and already read context for each query, use the appropriate parameters to get the most relevant results to anwer the queries.  # Input Information  1.  **Original Queries**: The queries need to be answered. ``` Query 0: Identify all exception classes defined , their inheritance hierarchy, and usage patterns to understand PyMySQL'sexceptiontaxonomy.Query1:Locatewhereandhowexceptionsareraisedwithincoremodules,mappingspecificerrortypestotriggerconditionslikeconnectionfailures,queryerrors,ordatacorruption.Query2:ExtractMySQLservererrorcodesandclienterrorcodestocorrelatePyMySQLexceptionswithunderlyingMySQLerrorconditionsforcomprehensivedocumentationoftriggerscenarios.```2.**AlreadyReadContext**:Thefileordirectorystructureofthecoderepositorymayhelpyoutodecidewhattosearchnext.-file_io:pymysql/err.py:```L0:importstructL2:from.constantsimportERL5:classMySQLError(Exception):L6:\"\"\"Exception related to operation with MySQL.\"\"\" L9: class Warning(Warning, MySQLError): L11:     while inserting, etc.\"\"\" L14: class Error(MySQLError): L16:     (not Warning).\"\"\" L19: class InterfaceError(Error): L21:     interface rather than the database itself.\"\"\" L24: class DatabaseError(Error): L26:     database.\"\"\" L29: class DataError(DatabaseError): L32:     etc.\"\"\" L35: class OperationalError(DatabaseError): L40:     error occurred during processing, etc.\"\"\" L43: class IntegrityError(DatabaseError): L46:     etc.\"\"\" L49: class InternalError(DatabaseError): L52:     out of sync, etc.\"\"\" L55: class ProgrammingError(DatabaseError): L58:     of parameters specified, etc.\"\"\" L61: class NotSupportedError(DatabaseError): L65:     has transactions turned off.\"\"\" L71: def _map_error(exc, *errors): L73:         error_map[error] = exc L136: def raise_mysql_exception(data): L149:     raise errorclass(errno, errval) ```  ---------- - file_io: pymysql/tests/test_err.py: ``` L0: import pytest L1: from pymysql import err L4: def test_raise_mysql_exception(): L15:     assert cm.value.args == (1040, \"Too many connections\") ```  ---------- - file_io: pymysql/connections.py: ``` L4: import errno L5: import os L6: import socket L7: import struct L8: import sys L9: import traceback L10: import warnings L12: from . import _auth L14: from .charset import charset_by_name, charset_by_id L15: from .constants import CLIENT, COMMAND, CR, ER, FIELD_TYPE, SERVER_STATUS L16: from . import converters L17: from .cursors import Cursor L18: from .optionfile import Parser L19: from .protocol import ( L26: ) L27: from . import err, VERSION_STRING L30:     import ssl L38:     import getpass L69: def _pack_int24(n): L70:     return struct.pack(\"<I\", n)[:3] L74: def _lenenc_int(i): L90:         ) L93: class Connection: L171:     def __init__( L250:             def _config(key, arg): L256:                     return arg L364:             self.connect() L366:     def __enter__(self): L367:         return self L369:     def __exit__(self, *exc_info): L371:         self.close() L373:     def _create_ssl_ctx(self, sslp): L411:         return ctx L413:     def close(self): L433:             self._force_close() L435:     @property L436:     def open(self): L438:         return self._sock is not None L440:     def _force_close(self): L450:         self._rfile = None L454:     def autocommit(self, value): L458:             self._send_autocommit_mode() L460:     def get_autocommit(self): L461:         return bool(self.server_status & SERVER_STATUS.SERVER_STATUS_AUTOCOMMIT) L463:     def _read_ok_packet(self): L472:         return ok L474:     def _send_autocommit_mode(self): L479:         self._read_ok_packet() L481:     def begin(self): L484:         self._read_ok_packet() L486:     def commit(self): L494:         self._read_ok_packet() L496:     def rollback(self): L504:         self._read_ok_packet() L506:     def show_warnings(self): L511:         return result.rows L513:     def select_db(self, db): L520:         self._read_ok_packet() L522:     def escape(self, obj, mapping=None): L534:         return converters.escape_item(obj, self.charset, mapping=mapping) L536:     def literal(self, obj): L541:         return self.escape(obj, self.encoders) L543:     def escape_string(self, s): L546:         return converters.escape_string(s) L548:     def _quote_bytes(self, s): L553:         return converters.escape_bytes(s) L555:     def cursor(self, cursor=None): L565:         return self.cursorclass(self) L568:     def query(self, sql, unbuffered=False): L575:         return self._affected_rows L577:     def next_result(self, unbuffered=False): L579:         return self._affected_rows L581:     def affected_rows(self): L582:         return self._affected_rows L584:     def kill(self, thread_id): L587:         self.query(f\"KILL {thread_id:d}\") L589:     def ping(self, reconnect=True): L612:                 raise L614:     def set_charset(self, charset): L620:         self.set_character_set(charset) L622:     def set_character_set(self, charset, collation=None): L640:         self.collation = collation L642:     def connect(self, sock=None): L727:             raise L729:     def write_packet(self, payload): L739:         self._next_seq_id = (self._next_seq_id + 1) % 256 L741:     def _read_packet(self, packet_type=MysqlPacket): L782:         return packet L784:     def _read_bytes(self, num_bytes): L807:         return data L809:     def _write_bytes(self, data): L817:             ) L819:     def _read_query_result(self, unbuffered=False): L829:         return result.affected_rows L831:     def insert_id(self): L835:             return 0 L837:     def _execute_command(self, command, sql): L878:                 break L880:     def _request_authentication(self): L988:             print(\"Succeed to auth\") ```  ---------- - file_io: pymysql/cursors.py: ``` L0: import re L1: import warnings L2: from . import err L16: class Cursor: L33:     def __init__(self, connection): L42:         self._rows = None L44:     def close(self): L55:             self.connection = None L57:     def __enter__(self): L58:         return self L60:     def __exit__(self, *exc_info): L62:         self.close() L64:     def _get_db(self): L67:         return self.connection L69:     def _check_executed(self): L71:             raise err.ProgrammingError(\"execute() first\") L73:     def _conv_row(self, row): L74:         return row L76:     def setinputsizes(self, *args): L77:         \"\"\"Does nothing, required by DB API.\"\"\" L79:     def setoutputsizes(self, *args): L80:         \"\"\"Does nothing, required by DB API.\"\"\" L82:     def _nextset(self, unbuffered=False): L94:         return True L96:     def nextset(self): L97:         return self._nextset(False) L99:     def _escape_args(self, args, conn): L107:             return conn.escape(args) L109:     def mogrify(self, query, args=None): L130:         return query L132:     def execute(self, query, args=None): L154:         return result L156:     def executemany(self, query, args): L191:         return self.rowcount L193:     def _do_execute_many( L221:         return rows L223:     def callproc(self, procname, args=()): L270:         return args L272:     def fetchone(self): L279:         return result L281:     def fetchmany(self, size=None): L291:         return result L293:     def fetchall(self): L303:         return result L305:     def scroll(self, value, mode=\"relative\"): L316:         self.rownumber = r L318:     def _query(self, q): L323:         return self.rowcount L325:     def _clear_result(self): L333:         self._rows = None L335:     def _do_get_result(self): L344:         self._rows = result.rows L346:     def __iter__(self): L347:         return self L349:     def __next__(self): L353:         return row L355:     def __getattr__(self, name): L377:         raise AttributeError(name) L380: class DictCursorMixin: L384:     def _do_get_result(self): L396:             self._rows = [self._conv_row(r) for r in self._rows] L398:     def _conv_row(self, row): L401:         return self.dict_type(zip(self._fields, row)) L404: class DictCursor(DictCursorMixin, Cursor): L405:     \"\"\"A cursor which returns results as a dictionary\"\"\" L408: class SSCursor(Cursor): L424:     def _conv_row(self, row): L425:         return row L427:     def close(self): L439:             self.connection = None L443:     def _query(self, q): L448:         return self.rowcount L450:     def nextset(self): L451:         return self._nextset(unbuffered=True) L453:     def read_next(self): L455:         return self._conv_row(self._result._read_rowdata_packet_unbuffered()) L457:     def fetchone(self): L465:         return row L467:     def fetchall(self): L473:         return list(self.fetchall_unbuffered()) L475:     def fetchall_unbuffered(self): L481:         return iter(self.fetchone, None) L483:     def fetchmany(self, size=None): L501:         return rows L503:     def scroll(self, value, mode=\"relative\"): L526:             raise err.ProgrammingError(\"unknown scroll mode %s\" % mode) L529: class SSDictCursor(DictCursorMixin, SSCursor): L530:     \"\"\"An unbuffered cursor, which returns results as a dictionary\"\"\" ```  ---------- - file_io: pymysql/constants/ER.py: ``` ERROR_FIRST = 1000  HASHCHK = 1000  NISAMCHK = 1001  NO = 1002  YES = 1003  CANT_CREATE_FILE = 1004  CANT_CREATE_TABLE = 1005  CANT_CREATE_DB = 1006  DB_CREATE_EXISTS = 1007  DB_DROP_EXISTS = 1008  DB_DROP_DELETE = 1009  DB_DROP_RMDIR = 1010  CANT_DELETE_FILE = 1011  CANT_FIND_SYSTEM_REC = 1012  CANT_GET_STAT = 1013  CANT_GET_WD = 1014  CANT_LOCK = 1015  CANT_OPEN_FILE = 1016  FILE_

Please process the input information and generate the result according to the instructions above."""

SNIPPETS_REORDER_PROMPT = """# Role
You are a professional code relevance and answerability assessment expert.

# Goal
Your single task is to reorder the `Code Snippets` based on their relevance to the `User Query`.

# Internal Thought Process (Do not show this in the output)
1.  **Analyze Dimensions**: First, mentally score the snippet from 0-100 on the following dimensions:
    * `Directness` (Weight: 50%): Does the code contain the direct answer or key implementation?
    * `Coverage` (Weight: 25%): How well does it cover the key points of the query?
    * `Confidence` (Weight: 15%): How explicit and verifiable is the information?
    * `Specificity` (Weight: 10%): Is it a specific implementation, or a more general example/config?

2.  **Calculate Score**: Then, mentally compute the total score using the formula:
    `score = round(0.50*Directness + 0.25*Coverage + 0.15*Confidence + 0.10*Specificity, 1)`

3. **Reorder the provided `Code Snippets` and return their scores**
    *   **Highest Score First**: The snippet with the highest score should be ranked first.
    *   **Only return the first {max_code_snippets} snippets**
    *   **Not return the content of code snippets, but return their index to refer to them 

# Input
User Query:
{query}

# Code Snippet:
{code_snippets}

# Output Requirement
Your output should be json dict format, the key is `index` string and the value is `score`, It **absolutely must not contain** any explanations, comments, prefixes, suffixes, or any other text outside the dict itself.

# Examples
{{"1": 0.9, "3": 0.85, "2": 0.8}}

Please process the input information and generate the result according to the instructions above."""

SYSTEM_PROMPTS = {
    "query_split": "You are a professional code analysis assistant, skilled at breaking down complex problems into specific searchable sub-problems.",
    "generate_new_query": "You are an intelligent assistant, good at analyzing current information and deciding whether further in-depth search is needed.",
    "reorder": "You are a professional code relevance and answerability assessment expert, able to accurately judge the relevance of code snippets to the query."
}