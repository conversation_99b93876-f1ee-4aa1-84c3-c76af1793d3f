from typing import List, Optional, Literal
from pydantic import BaseModel, Field

# 任务状态枚举
TaskStatus = Literal[
    'pending_upload',
    'received',
    'processing',
    'running',
    'success',
    'failed',
    'rolled_back',
    'cancelled'
]

class ProjectCreateRequest(BaseModel):
    """项目创建请求"""
    username: str = Field(description="用户名")
    source: str = Field(description="源")
    branch: str = Field(description="分支")
    repo_url: str = Field(description="仓库URL")


class ProjectCreateResponse(BaseModel):
    """项目创建响应"""
    project_id: str = Field(description="项目ID")
    is_new: bool = Field(description="是否为新项目")
    root_hash: str = Field(description="根哈希")


class MerkleNode(BaseModel):
    """Merkle树节点"""
    path: str = Field(description="路径")
    hash: str = Field(description="哈希值")
    is_file: bool = Field(description="是否为文件")
    children: Optional[List['MerkleNode']] = Field(default=None, description="子节点")


class CompareRequest(BaseModel):
    """比较请求"""
    merkle_tree: MerkleNode = Field(description="Merkle树")


class DiffFiles(BaseModel):
    """差异文件"""
    added: List[str] = Field(description="新增文件")
    modified: List[str] = Field(description="修改文件")
    deleted: List[str] = Field(description="删除文件")
    reset_required: bool = Field(description="是否需要重置")
    multipart_upload: bool = Field(description="是否多部分上传")


class CompareResponse(BaseModel):
    """比较响应"""
    task_id: str = Field(description="任务ID")
    task_status: TaskStatus = Field(description="任务状态")
    chunk_version: int = Field(description="块版本")
    diff_files: DiffFiles = Field(description="差异文件")


class FileUploadItem(BaseModel):
    """文件上传项"""
    file_path: str = Field(description="文件路径")
    file_content: str = Field(description="文件内容")
    file_hash: str = Field(description="文件哈希")


class UploadRequest(BaseModel):
    """上传请求"""
    files: List[FileUploadItem] = Field(description="文件列表")
    auto_commit: bool = Field(description="是否自动提交")


class FileUploadResult(BaseModel):
    """文件上传结果"""
    file_path: str = Field(description="文件路径")
    status: Literal['pending', 'processing', 'completed', 'failed'] = Field(description="状态")
    message: Optional[str] = Field(default=None, description="消息")





class BulkUploadResponse(BaseModel):
    """批量上传响应"""
    task_id: str = Field(description="任务ID")
    results: List[FileUploadResult] = Field(description="结果列表")
    overall_status: str = Field(description="总体状态")
    task_status: TaskStatus = Field(description="任务状态")


class UploadProgress(BaseModel):
    """上传进度"""
    total_files: int = Field(description="总文件数")
    uploaded_files: int = Field(description="已上传文件数")
    progress: float = Field(description="进度")


class ProcessingProgress(BaseModel):
    """处理进度"""
    total_files: int = Field(description="总文件数")
    completed_files: int = Field(description="已完成文件数")
    progress_percentage: float = Field(description="进度百分比")


TaskPhase = Literal['uploading', 'received', 'processing', 'success', 'failed']


class TaskStatusResponse(BaseModel):
    """任务状态响应"""
    task_id: str = Field(description="任务ID")
    status: TaskStatus = Field(description="状态")
    parse_status: TaskStatus = Field(description="解析状态")
    stone_status: TaskStatus = Field(description="石头状态")
    created_at: str = Field(description="创建时间")
    updated_at: str = Field(description="更新时间")
    expires_at: str = Field(description="过期时间")
    start_at: Optional[str] = Field(default=None, description="开始时间")
    end_at: Optional[str] = Field(default=None, description="结束时间")
    is_expired: bool = Field(description="是否过期")
    is_terminal: bool = Field(description="是否终止")
    error_message: Optional[str] = Field(default=None, description="错误消息")
    phase: Optional[TaskPhase] = Field(default=None, description="阶段")
    upload_progress: Optional[UploadProgress] = Field(default=None, description="上传进度")
    processing_progress: Optional[ProcessingProgress] = Field(default=None, description="处理进度")


class QueryRequest(BaseModel):
    """查询请求"""
    query: str = Field(description="查询内容")
    project_id: str = Field(description="项目ID")
    retrieval_type: Optional[str] = Field(default=None, description="检索类型")
    top_k: Optional[int] = Field(default=None, description="返回结果数量")
    score_threshold: Optional[float] = Field(default=None, description="分数阈值")
    rewrite: Optional[bool] = Field(default=None, description="是否重写")


class VectorSearchMetadata(BaseModel):
    """向量搜索元数据"""
    chunk_id: str = Field(description="块ID")
    file_path: str = Field(description="文件路径")
    entry_type: str = Field(description="条目类型")
    location: List[int] = Field(description="位置")


class VectorSearchResult(BaseModel):
    """向量搜索结果"""
    doc_id: str = Field(description="文档ID")
    score: float = Field(description="分数")
    metadata: VectorSearchMetadata = Field(description="元数据")


class QueryResponse(BaseModel):
    """查询响应"""
    documents: List[VectorSearchResult] = Field(description="文档列表")


class DeleteTaskResponse(BaseModel):
    """删除任务响应"""
    task_id: str = Field(description="任务ID")
    status: TaskStatus = Field(description="状态")
    message: str = Field(description="消息")


class ServerConfigResponse(BaseModel):
    """服务器配置响应"""
    default_ignore: List[str] = Field(description="默认忽略列表")
    default_ignore_files: List[str] = Field(description="默认忽略文件列表")
    supported_extensions: List[str] = Field(description="支持的扩展名列表")
    max_file_size: int = Field(description="最大文件大小")
    max_file_count: int = Field(description="最大文件数量")


# 更新前向引用
MerkleNode.model_rebuild()