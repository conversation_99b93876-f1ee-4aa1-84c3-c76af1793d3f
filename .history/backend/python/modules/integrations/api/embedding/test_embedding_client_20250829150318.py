#!/usr/bin/env python3
"""
测试 embedding_client.py 的基本功能
"""

import asyncio
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../..'))

from modules.integrations.api.embedding.embedding_client import ApiClient, EmbeddingClient
from modules.integrations.api.embedding.schemas import (
    ApiClientConfig,
    ProjectCreateRequest,
    QueryRequest
)


async def test_api_client_initialization():
    """测试 API 客户端初始化"""
    print("测试 API 客户端初始化...")
    
    # 测试使用默认配置
    try:
        client = ApiClient()
        print(f"✓ 默认配置初始化成功: {client.base_url}")
    except Exception as e:
        print(f"✗ 默认配置初始化失败: {e}")
    
    # 测试使用自定义配置
    try:
        config = ApiClientConfig(
            server_url="http://test.example.com",
            api_prefix="/api/v1",
            timeout=60,
            api_key="test-key"
        )
        client = ApiClient(config)
        print(f"✓ 自定义配置初始化成功: {client.base_url}")
    except Exception as e:
        print(f"✗ 自定义配置初始化失败: {e}")


def test_schemas():
    """测试数据模型"""
    print("\n测试数据模型...")
    
    try:
        # 测试 ProjectCreateRequest
        request = ProjectCreateRequest(
            username="test_user",
            source="github",
            branch="main",
            repo_url="https://github.com/test/repo.git"
        )
        print(f"✓ ProjectCreateRequest 创建成功: {request.username}")
        
        # 测试 QueryRequest
        query = QueryRequest(
            query="test query",
            project_id="test-project-123",
            top_k=10,
            score_threshold=0.5
        )
        print(f"✓ QueryRequest 创建成功: {query.query}")
        
    except Exception as e:
        print(f"✗ 数据模型测试失败: {e}")


def test_embedding_client_compatibility():
    """测试 EmbeddingClient 向后兼容性"""
    print("\n测试 EmbeddingClient 向后兼容性...")
    
    try:
        # 测试旧的初始化方式
        client = EmbeddingClient()
        print(f"✓ EmbeddingClient 初始化成功: {client.base_url}")
        
        # 测试带参数的初始化（应该被忽略）
        client = EmbeddingClient(embedding_config="ignored")
        print(f"✓ EmbeddingClient 带参数初始化成功: {client.base_url}")
        
    except Exception as e:
        print(f"✗ EmbeddingClient 兼容性测试失败: {e}")


async def main():
    """主测试函数"""
    print("开始测试 embedding_client 模块...\n")
    
    await test_api_client_initialization()
    test_schemas()
    test_embedding_client_compatibility()
    
    print("\n测试完成!")


if __name__ == "__main__":
    asyncio.run(main())
