
import json
from typing import Optional, TypeVar, Type
import aiohttp
from pydantic import BaseModel, Field
from core.config import EmbeddingConfig
from modules.integrations.api.embedding.schemas import (
    ProjectCreateRequest,
    ProjectCreateResponse,
    CompareRequest,
    CompareResponse,
    UploadRequest,
    BulkUploadResponse,
    TaskStatusResponse,
    QueryRequest,
    QueryResponse,
    DeleteTaskResponse,
    ServerConfigResponse,
)

T = TypeVar('T')


class ApiClient:
    """API客户端，用于与嵌入服务进行通信"""

    def __init__(self, config: EmbeddingConfig = None):
        """
        初始化API客户端

        Args:
            config: API客户端配置，如果为None则使用默认配置
        """
        
        self.config = config

        self.base_url = f"{self.config.server_url}{self.config.api_prefix}"

    async def create_project(self, request: ProjectCreateRequest) -> ProjectCreateResponse:
        """
        在服务器上创建新项目

        Args:
            request: 项目创建请求

        Returns:
            ProjectCreateResponse: 项目创建响应
        """
        return await self._make_request(
            method="POST",
            endpoint="/project",
            data=request.model_dump(),
            response_type=ProjectCreateResponse
        )

    async def compare_with_server(
        self,
        project_id: str,
        request: CompareRequest
    ) -> CompareResponse:
        """
        将本地 Merkle 树与服务器版本比较

        Args:
            project_id: 项目ID
            request: 比较请求

        Returns:
            CompareResponse: 比较响应
        """
        return await self._make_request(
            method="POST",
            endpoint=f"/project/{project_id}/compare",
            data=request.model_dump(),
            response_type=CompareResponse
        )

    async def upload_files(
        self,
        task_id: str,
        request: UploadRequest
    ) -> BulkUploadResponse:
        """
        上传文件

        Args:
            task_id: 任务ID
            request: 上传请求

        Returns:
            BulkUploadResponse: 批量上传响应
        """
        return await self._make_request(
            method="POST",
            endpoint=f"/codebase-task/{task_id}/upload",
            data=request.model_dump(),
            response_type=BulkUploadResponse
        )

    async def get_task_status(self, task_id: str) -> TaskStatusResponse:
        """
        获取任务处理状态

        Args:
            task_id: 任务ID

        Returns:
            TaskStatusResponse: 任务状态响应
        """
        return await self._make_request(
            method="GET",
            endpoint=f"/codebase-task/{task_id}/status",
            data=None,
            response_type=TaskStatusResponse
        )

    async def query_codebase(self, request: QueryRequest) -> QueryResponse:
        """
        查询代码库

        Args:
            request: 查询请求

        Returns:
            QueryResponse: 查询响应
        """
        return await self._make_request(
            method="POST",
            endpoint="/retrieval/recall",
            data=request.model_dump(),
            response_type=QueryResponse
        )

    async def delete_task(self, task_id: str) -> DeleteTaskResponse:
        """
        取消任务

        Args:
            task_id: 任务ID

        Returns:
            DeleteTaskResponse: 删除任务响应
        """
        return await self._make_request(
            method="DELETE",
            endpoint=f"/codebase-task/{task_id}/cancel",
            data={},
            response_type=DeleteTaskResponse
        )

    async def get_server_config(self) -> ServerConfigResponse:
        """
        从服务端获取配置

        Returns:
            ServerConfigResponse: 服务器配置响应
        """
        return await self._make_request(
            method="GET",
            endpoint="/config",
            data=None,
            response_type=ServerConfigResponse
        )

    async def _make_request(
        self,
        method: str,
        endpoint: str,
        data: Optional[dict],
        response_type: Type[T]
    ) -> T:
        """
        发起 HTTP 请求

        Args:
            method: HTTP方法
            endpoint: API端点
            data: 请求数据
            response_type: 响应类型

        Returns:
            T: 响应对象

        Raises:
            aiohttp.ClientError: 网络请求错误
            ValueError: 响应解析错误
        """
        url = f"{self.base_url}{endpoint}"

        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'CodeIndexMCP/1.0',
        }

        # 添加 API 密钥认证
        if self.config.api_key:
            headers['Authorization'] = f'Bearer {self.config.api_key}'

        timeout = aiohttp.ClientTimeout(total=self.config.timeout)

        async with aiohttp.ClientSession(timeout=timeout) as session:
            try:
                if method.upper() == 'GET':
                    async with session.get(url, headers=headers) as response:
                        return await self._handle_response(response, response_type)
                elif method.upper() == 'POST':
                    async with session.post(url, headers=headers, json=data) as response:
                        return await self._handle_response(response, response_type)
                elif method.upper() == 'DELETE':
                    async with session.delete(url, headers=headers, json=data) as response:
                        return await self._handle_response(response, response_type)
                else:
                    raise ValueError(f"Unsupported HTTP method: {method}")

            except aiohttp.ClientError as e:
                raise aiohttp.ClientError(f"Request failed: {e}")

    async def _handle_response(
        self,
        response: aiohttp.ClientResponse,
        response_type: Type[T]
    ) -> T:
        """
        处理HTTP响应

        Args:
            response: HTTP响应
            response_type: 响应类型

        Returns:
            T: 响应对象

        Raises:
            aiohttp.ClientResponseError: HTTP错误状态码
            ValueError: 响应解析错误
        """
        if response.status >= 200 and response.status < 300:
            try:
                response_text = await response.text()
                if response_text:
                    response_data = json.loads(response_text)
                else:
                    response_data = {}
                return response_type(**response_data)
            except json.JSONDecodeError as e:
                raise ValueError(f"Failed to parse response: {e}")
            except Exception as e:
                raise ValueError(f"Failed to create response object: {e}")
        else:
            error_text = await response.text()
            raise aiohttp.ClientResponseError(
                request_info=response.request_info,
                history=response.history,
                status=response.status,
                message=f"HTTP {response.status}: {error_text}"
            )


# 为了向后兼容，保留原有的 EmbeddingClient 类
class EmbeddingClient(ApiClient):
    """嵌入客户端，继承自ApiClient以保持向后兼容性"""

    def __init__(self, config: Optional[ApiClientConfig] = None):
        """
        初始化嵌入客户端

        Args:
            config: API客户端配置，如果为None则使用默认配置
        """
        super().__init__(config)
