from modules.common.constants import IOToolEnum
from modules.integrations.tools.io.file_io import FileIOTool
from modules.integrations.tools.io.directory_io import DirectoryIOTool
from utils.trace_logger import get_trace_logger

trace_logger = get_trace_logger(__name__)

class AnyIOTool:
    def __init__(self, repo_path: str):
        # 使用对象的原因一方面是希望隔离不同请求之间的空间，另一方面是可以兼容带有状态的search过程
        # 当前将repo_path作为self属性是假设所有的读写操作发生在用户项目空间内，但是该假设未必成立，有可能要读取IDE的应用目录下的文件
        self.repo_path = repo_path

        self.file_io_tool = FileIOTool(self.repo_path)
        self.directory_io_tool = DirectoryIOTool(self.repo_path)

    def read(self, query: str, io_tool: IOToolEnum,  **kwargs) -> str:
        if io_tool == IOToolEnum.FILE:
            try:
                return self.file_io_tool.read(query, **kwargs)
            except Exception as e:
                trace_logger.error(f"Failed to read file: {e}")
                return se
        elif io_tool == IOToolEnum.DIRECTORY:
            return self.directory_io_tool.read(query, **kwargs)
        else:
            raise ValueError("未知的IO模式")
    
    def write(self, query: str, io_tool: IOToolEnum, **kwargs) -> None:
        if io_tool == IOToolEnum.FILE:
            return self.file_io_tool.write(query, **kwargs)
        else:
            raise ValueError("未知的IO模式")
        
    @property
    def description(self):
        # 将所有可用的io_tool的description进行拼接
        descriptions = []

        # 添加文件IO工具描述
        descriptions.append(self.file_io_tool.description)

        # 添加目录IO工具描述
        descriptions.append(self.directory_io_tool.description)

        return "\n---\n".join(descriptions)
    
    @property
    def examples(self):
        # 将所有可用的io_tool的examples进行拼接
        examples = []

        # 添加文件IO工具示例
        examples.append("<!-- File I/O Examples -->")
        examples.append(self.file_io_tool.examples)

        # 添加目录IO工具示例
        examples.append("<!-- Directory I/O Examples -->")
        examples.append(self.directory_io_tool.examples)

        return "\n---\n".join(examples)
