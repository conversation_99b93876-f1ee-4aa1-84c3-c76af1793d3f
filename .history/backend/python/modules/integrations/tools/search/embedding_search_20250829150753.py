from typing import List

from modules.integrations.tools.search.any_search import CodeSnippet, SearchToolABC
from modules.integrations.apis.embedding.embedding_client import EmbeddingClient


class EmbeddingSearchTool(SearchToolABC):
    def __init__(self, repo_path: str, refresh: bool = True, ):
        self.repo_path = repo_path
        self.embedding_client = EmbeddingClient()

        # 创建或获取项目

        # 如果 refresh 或者 is_new 开始比较项目并且上传项目，等待项目完成
    
    def upload_project(self):
        pass
    
    def refresh_project(self):
        pass

    def search(self, query: str, **kwargs) -> List[CodeSnippet]:
        pass
        
