import os
import asyncio
import hashlib
from typing import List, Optional

from modules.integrations.tools.search.abc_search import SearchToolABC
from modules.common.schema import CodeSnippet
from modules.integrations.apis.embedding.embedding_client import EmbeddingClient
from modules.integrations.apis.embedding.schemas import (
    ProjectCreateRequest,
    ProjectCreateResponse,
    CompareRequest,
    UploadRequest,
    QueryRequest,
    MerkleNode,
    FileUploadItem,
    VectorSearchResult,
)
from utils.file import build_file_tree, build_file_list, read_chunk_contents
from modules.common.constants import FileFilterMode
from utils.trace_logger import get_trace_logger
from utils.convert import safe_parse_xml_with_preprocessing, extract_text_from_xml_element

trace_logger = get_trace_logger(__name__)


class EmbeddingSearchTool(SearchToolABC):
    def __init__(self, repo_path: str, refresh: bool = True):
        self.repo_path = repo_path
        self.embedding_client = EmbeddingClient()
        self.project_id: Optional[str] = None

        # 检查是否在事件循环中，选择合适的初始化方式
        try:
            # 尝试获取当前事件循环
            asyncio.get_running_loop()
            # 如果已经在事件循环中，延迟初始化
            trace_logger.warning("EmbeddingSearchTool initialized in async context, project will be initialized on first search")
            self._initialized = False
            self._refresh = refresh
        except RuntimeError:
            # 没有运行的事件循环，使用asyncio.run
            asyncio.run(self._initialize_project(refresh))
            self._initialized = True

    async def _initialize_project(self, refresh: bool = True):
        """初始化项目，创建或获取项目ID"""
        try:
            # 创建或获取项目
            project_response = await self._create_or_get_project()
            self.project_id = project_response.project_id

            # 如果 refresh 或者 is_new 开始比较项目并且上传项目，阻塞等待项目完成
            if refresh or project_response.is_new:
                await self._upload_and_wait_completion()

        except Exception as e:
            trace_logger.error(f"Failed to initialize project: {e}")
            raise

    async def _create_or_get_project(self) -> ProjectCreateResponse:
        """创建或获取项目"""
        # 生成项目请求
        request = ProjectCreateRequest(
            username="codebase",  # 可以从配置中获取
            source="local",
            branch="main",  # 可以从git信息中获取
            repo_url=f"file://{self.repo_path}"
        )

        return await self.embedding_client.create_project(request)

    async def _upload_and_wait_completion(self):
        """上传项目文件并等待完成"""
        try:
            # 构建Merkle树
            merkle_tree = self._build_merkle_tree()

            # 比较与服务器的差异
            compare_request = CompareRequest(merkle_tree=merkle_tree)
            compare_response = await self.embedding_client.compare_with_server(
                self.project_id, compare_request
            )

            # 如果有差异文件，上传它们
            diff_files = compare_response.diff_files
            if diff_files.added or diff_files.modified:
                await self._upload_files(compare_response.task_id, diff_files)
                await self._wait_for_completion(compare_response.task_id)

        except Exception as e:
            trace_logger.error(f"Failed to upload and process project: {e}")
            raise

    def _build_merkle_tree(self) -> MerkleNode:
        """构建项目的Merkle树"""
        def build_node(file_node) -> MerkleNode:
            if file_node.type == "file":
                # 计算文件哈希
                file_hash = self._calculate_file_hash(file_node.path)
                return MerkleNode(
                    path=file_node.name,
                    hash=file_hash,
                    is_file=True,
                    children=[]
                )
            else:
                # 目录节点
                children = [build_node(child) for child in file_node.children]
                # 目录哈希基于子节点哈希
                dir_hash = self._calculate_directory_hash(children)
                return MerkleNode(
                    path=file_node.name,
                    hash=dir_hash,
                    is_file=False,
                    children=children
                )

        # 获取文件树
        file_nodes = build_file_tree(
            root_dir=self.repo_path,
            start_dir=self.repo_path,
            filter_mode=FileFilterMode.EMBEDDING
        )

        # 构建根节点
        if len(file_nodes) == 1:
            return build_node(file_nodes[0])
        else:
            # 多个根节点，创建虚拟根节点
            children = [build_node(node) for node in file_nodes]
            root_hash = self._calculate_directory_hash(children)
            return MerkleNode(
                path=".",
                hash=root_hash,
                is_file=False,
                children=children
            )

    def _calculate_file_hash(self, file_path: str) -> str:
        """计算文件内容的哈希值"""
        try:
            full_path = os.path.join(self.repo_path, file_path)
            with open(full_path, 'rb') as f:
                content = f.read()
                return hashlib.sha256(content).hexdigest()
        except Exception as e:
            trace_logger.warning(f"Failed to calculate hash for {file_path}: {e}")
            # 返回基于文件路径的哈希作为fallback
            return hashlib.sha256(file_path.encode()).hexdigest()

    def _calculate_directory_hash(self, children: List[MerkleNode]) -> str:
        """计算目录哈希（基于子节点哈希）"""
        if not children:
            return hashlib.sha256(b"").hexdigest()

        # 按路径排序确保一致性
        sorted_children = sorted(children, key=lambda x: x.path)
        combined_hash = "".join([child.hash for child in sorted_children])
        return hashlib.sha256(combined_hash.encode()).hexdigest()

    async def _upload_files(self, task_id: str, diff_files):
        """上传差异文件"""
        files_to_upload = []

        # 读取需要上传的文件内容
        all_files = diff_files.added + diff_files.modified
        file_contents = build_file_list(
            root_dir=self.repo_path,
            start_dir=self.repo_path,
            filter_mode=FileFilterMode.LOCAL
        )

        for file_path in all_files:
            if file_path in file_contents:
                file_hash = self._calculate_file_hash(file_path)
                files_to_upload.append(FileUploadItem(
                    file_path=file_path,
                    file_content=file_contents[file_path],
                    file_hash=file_hash
                ))

        if files_to_upload:
            upload_request = UploadRequest(
                files=files_to_upload,
                auto_commit=True
            )
            await self.embedding_client.upload_files(task_id, upload_request)

    async def _wait_for_completion(self, task_id: str, max_wait_time: int = 300):
        """等待任务完成"""
        import time
        start_time = time.time()

        while time.time() - start_time < max_wait_time:
            try:
                status_response = await self.embedding_client.get_task_status(task_id)

                if status_response.is_terminal:
                    if status_response.status == "success":
                        trace_logger.info(f"Task {task_id} completed successfully")
                        return
                    else:
                        error_msg = status_response.error_message or "Unknown error"
                        raise Exception(f"Task {task_id} failed: {error_msg}")
                
                trace_logger.info(f"Waiting for task {task_id} to complete, current status: {status_response.status}")
                # 等待一段时间后重试
                await asyncio.sleep(5)

            except Exception as e:
                trace_logger.error(f"Error checking task status: {e}")
                await asyncio.sleep(5)

        raise TimeoutError(f"Task {task_id} did not complete within {max_wait_time} seconds")

    def search(self, query: str, **kwargs) -> List[CodeSnippet]:
        """同步搜索接口"""
        return asyncio.run(self.search_async(query, **kwargs))

    async def search_async(self, query: str, **kwargs) -> List[CodeSnippet]:
        """异步搜索代码库"""
        # 如果还未初始化，先进行初始化
        if not hasattr(self, '_initialized') or not self._initialized:
            await self._initialize_project(getattr(self, '_refresh', True))
            self._initialized = True

        if not self.project_id:
            raise ValueError("Project not initialized")

        try:
            # 解析查询参数
            search_params = self._parse_query(query)

            # 合并kwargs参数（kwargs优先级更高）
            query_text = search_params['query']
            top_k = 20
            score_threshold = kwargs.get('score_threshold', search_params.get('score_threshold', 0.5))
            retrieval_type = kwargs.get('retrieval_type', search_params.get('retrieval_type', 'chunk'))
            rewrite = kwargs.get('rewrite', search_params.get('rewrite', False))

            query_request = QueryRequest(
                query=query_text,
                project_id=self.project_id,
                retrieval_type=retrieval_type,
                top_k=top_k,
                score_threshold=score_threshold,
                rewrite=rewrite
            )

            # 执行查询
            query_response = await self.embedding_client.query_codebase(query_request)

            # 转换结果为CodeSnippet
            return self._convert_to_code_snippets(query_response.documents)

        except Exception as e:
            trace_logger.error(f"Search failed: {e}")
            return []

    def _parse_query(self, query: str) -> dict:
        """
        解析查询参数，支持XML格式和普通文本

        支持以下XML格式：
        1. 普通文本: <query>查询内容</query>
        2. CDATA格式: <query><![CDATA[包含<>等特殊字符的查询内容]]></query>
        3. HTML实体转义: <query>包含&lt;等转义字符的查询内容</query>

        Args:
            query: 查询字符串

        Returns:
            dict: 解析后的参数字典
        """
        # 默认参数
        params = {
            'query': query.strip(),
            'top_k': 10
        }

        # 尝试解析XML格式
        if query.strip().startswith('<embedding>'):
            try:
                # 使用通用的XML预处理和解析函数
                root = safe_parse_xml_with_preprocessing(query.strip(), 'query')

                if root is not None:
                    # 提取查询内容
                    query_elem = root.find('query')
                    if query_elem is not None:
                        query_content = extract_text_from_xml_element(query_elem)
                        if query_content:
                            params['query'] = query_content

                    # 提取top_k参数
                    top_k_elem = root.find('top_k')
                    if top_k_elem is not None and top_k_elem.text:
                        try:
                            params['top_k'] = int(top_k_elem.text.strip())
                        except ValueError:
                            trace_logger.warning(f"Invalid top_k value: {top_k_elem.text}, using default 10")

            except Exception as e:
                trace_logger.warning(f"Failed to parse XML query, using as plain text: {e}")

        return params

    def _convert_to_code_snippets(self, documents: List[VectorSearchResult]) -> List[CodeSnippet]:
        """将向量搜索结果转换为CodeSnippet"""
        snippets: List[CodeSnippet] = []

        for doc in documents:
            try:
                metadata = doc.metadata

                # 解析位置信息
                start_line = metadata.location[0] if metadata.location else 0
                end_line = metadata.location[1] if len(metadata.location) > 1 else start_line


                snippet = CodeSnippet(
                    file_path=metadata.file_path,
                    start_line=start_line,
                    end_line=end_line,
                    content="",
                    context_before="",  # 可以根据需要添加上下文
                    context_after="",   # 可以根据需要添加上下文
                    score=doc.score
                )
                snippets.append(snippet)

            except Exception as e:
                trace_logger.warning(f"Failed to convert document to snippet: {e}")
                continue
        
        chunk_contents = read_chunk_contents(self.repo_path, [f"{snippet.file_path}:{snippet.start_line}-{snippet.end_line}" for snippet in snippets])

        for snippet_idx, snippet in enumerate(snippets):
            snippets[snippet_idx].content = chunk_contents[f"{snippet.file_path}:{snippet.start_line}-{snippet.end_line}"]

        return snippets
    
    @property
    def description(self) -> str:
        """搜索工具描述"""
        return """- `embedding`: Vector embedding-based semantic search engine for conceptual and cross-language code discovery. Designed for finding code based on functionality, concepts, or semantic similarity rather than exact keyword matches.

**Primary Use Cases**:
1. **Conceptual Search**: When you want to find code that implements specific functionality or concepts, even if you don't know the exact terms used
2. **Cross-Language Discovery**: Finding similar implementations across different programming languages
3. **Fuzzy Functionality Search**: When you have a general idea of what you're looking for but lack specific keywords
4. **Code Understanding**: Finding code snippets that are semantically similar to a given description or example

**When to Use This Tool**:
- ✅ You want to find code that implements specific functionality or concepts
- ✅ You have a natural language description of what you need
- ✅ You want to understand how certain concepts are implemented
- ✅ Traditional keyword search isn't finding what you need
- ❌ Don't use for exact string matching or regex patterns

**Parameters**:
- query: Natural language description of functionality or concepts you're looking for
- top_k: (optional) Maximum results to return (default: 10)

**Query Style**: Use natural language to describe the functionality, concept, or type of code you're looking for. Be descriptive about what the code should do rather than how it should be implemented."""

    @property
    def examples(self) -> str:
        """搜索示例"""
        return """<output>
    <embedding>
    <query>database connection and query execution</query>
    <top_k>15</top_k>
    </embedding>

    <embedding>
    <query>user authentication and password validation</query>
    </embedding>

    <embedding>
    <query>file upload handling with error checking</query>
    </embedding>
</output>"""