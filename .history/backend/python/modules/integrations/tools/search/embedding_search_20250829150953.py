import os
import asyncio
import hashlib
from typing import List, Optional
from pathlib import Path

from modules.integrations.tools.search.abc_search import Search<PERSON>oolABC
from modules.common.schema import CodeSnippet
from modules.integrations.apis.embedding.embedding_client import EmbeddingClient
from modules.integrations.apis.embedding.schemas import (
    ProjectCreateRequest,
    ProjectCreateResponse,
    CompareRequest,
    CompareResponse,
    UploadRequest,
    BulkUploadResponse,
    TaskStatusResponse,
    QueryRequest,
    QueryResponse,
    MerkleNode,
    FileUploadItem,
    VectorSearchResult,
)
from utils.file import build_file_tree, build_file_list
from modules.common.constants import FileFilterMode
from utils.trace_logger import get_trace_logger

logger = get_trace_logger(__name__)


class EmbeddingSearchTool(SearchToolABC):
    def __init__(self, repo_path: str, refresh: bool = True):
        self.repo_path = repo_path
        self.embedding_client = EmbeddingClient()
        self.project_id: Optional[str] = None

        # 异步初始化项目
        asyncio.run(self._initialize_project(refresh))

    async def _initialize_project(self, refresh: bool = True):
        """初始化项目，创建或获取项目ID"""
        try:
            # 创建或获取项目
            project_response = await self._create_or_get_project()
            self.project_id = project_response.project_id

            # 如果 refresh 或者 is_new 开始比较项目并且上传项目，阻塞等待项目完成
            if refresh or project_response.is_new:
                await self._upload_and_wait_completion()

        except Exception as e:
            logger.error(f"Failed to initialize project: {e}")
            raise

    async def _create_or_get_project(self) -> ProjectCreateResponse:
        """创建或获取项目"""
        repo_name = os.path.basename(self.repo_path)

        # 生成项目请求
        request = ProjectCreateRequest(
            username="default_user",  # 可以从配置中获取
            source="local",
            branch="main",  # 可以从git信息中获取
            repo_url=f"file://{self.repo_path}"
        )

        return await self.embedding_client.create_project(request)
        
