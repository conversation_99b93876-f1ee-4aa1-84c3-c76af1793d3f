from typing import List

from modules.integrations.tools.search.any_search import CodeSnippet, SearchToolABC
from modules.integrations.apis.embedding.embedding_client import EmbeddingClient


class EmbeddingSearchTool(SearchToolABC):
    def __init__(self, repo_path: str, refresh: bool = True, ):
        self.repo_path = repo_path
        self.embedding_client = EmbeddingClient()

        # 创建或获取
    
    def upload_project(self):
        pass
    
    def refresh_project(self):
        pass

    def search(self, query: str, **kwargs) -> List[CodeSnippet]:
        pass
        
