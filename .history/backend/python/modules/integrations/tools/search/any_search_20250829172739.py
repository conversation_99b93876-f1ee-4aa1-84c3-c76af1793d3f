from typing import List

from core.config import get_config
from modules.integrations.tools.search.abc_search import SearchToolABC
from modules.common.constants import SearchToolEnum
from modules.common.schema import CodeSnippet
from modules.integrations.tools.search.search_factory import get_search_tool_instance
from utils.trace_logger import get_trace_logger
trace_logger = get_trace_logger(__name__)

class AnySearchTool(SearchToolABC):
    def __init__(self, repo_path: str, enabled_search_tools: List[str] = get_config().deepsearch.enabled_search_tools):
        
        self.repo_path = repo_path
        self.enabled_search_tools = [SearchToolEnum.COMPELETE] # 强制使用COMPELETE
        for tool in enabled_search_tools:
            try:
                self.enabled_search_tools.append(SearchToolEnum(tool))
            except ValueError:
                pass

    def search(self, query: str, search_tool: SearchToolEnum, **kwargs) -> List[CodeSnippet]:
        if search_tool not in self.enabled_search_tools:
            raise ValueError(f"搜索工具{search_tool}未启用")
        
        if search_tool == SearchToolEnum.GREP:
            return get_search_tool_instance(SearchToolEnum.GREP, self.repo_path).search(query, **kwargs)
        elif search_tool == SearchToolEnum.INVERTED_INDEX:
            return get_search_tool_instance(SearchToolEnum.INVERTED_INDEX, self.repo_path).search(query, **kwargs)
        elif search_tool == SearchToolEnum.TERM_SPRSE:
            return get_search_tool_instance(SearchToolEnum.TERM_SPRSE, self.repo_path).search(query, **kwargs)
        elif search_tool == SearchToolEnum.EMBEDDING:
            return get_search_tool_instance(SearchToolEnum.EMBEDDING, self.repo_path).search(query, **kwargs)
        
        else:
            raise ValueError("未知搜索工具")

    async def search_async(self, query: str, search_tool: SearchToolEnum, **kwargs) -> List[CodeSnippet]:
        if search_tool not in self.enabled_search_tools:
            raise ValueError(f"搜索工具{search_tool}未启用")

        search_instance = get_search_tool_instance(search_tool, self.repo_path)

        # 检查搜索工具是否支持异步
        if hasattr(search_instance, 'search_async') and callable(getattr(search_instance, 'search_async')):
            return await search_instance.search_async(query, **kwargs)
        else:
            # 如果不支持异步，使用线程池执行同步方法
            import asyncio
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, lambda: search_instance.search(query, **kwargs))

    @property
    def description(self):
        # 将所有可用的search_tool的description进行拼接
        descriptions = []
        descriptions.append("Unified search interface that provides access to multiple search engines. Choose the most appropriate search tool based on your query type and requirements.\n")

        for tool_enum in self.enabled_search_tools:
            try:
                search_tool = get_search_tool_instance(tool_enum, self.repo_path)
                descriptions.append(f"## Search tool description: {tool_enum.value}")
                tool_description = search_tool.description
                descriptions.append(tool_description)
                descriptions.append("---")  
                descriptions.append("") # 添加空行分隔
            except Exception as e:
                trace_logger.info(f"Failed to get description for {tool_enum.value}", e)
                # 如果某个工具初始化失败，跳过它
                continue

        return "\n".join(descriptions)

    @property
    def examples(self):
        # 将所有可用的search_tool的examples进行拼接
        examples = []
        
        for tool_enum in self.enabled_search_tools:
            try:
                search_tool = get_search_tool_instance(tool_enum, self.repo_path)
                examples.append(f"## Search tool examples: {tool_enum.value}")
                tool_examples = search_tool.examples
                examples.append(tool_examples)
                examples.append("---")
                examples.append("")  # 添加空行分隔
            except Exception as e:
                # 如果某个工具初始化失败，跳过它
                trace_logger.info(f"Failed to get examples for {tool_enum.value}", e)
                continue

        return "\n".join(examples)
