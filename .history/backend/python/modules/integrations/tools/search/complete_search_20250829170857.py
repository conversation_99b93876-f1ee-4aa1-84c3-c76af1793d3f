import os
from typing import List
from modules.common.schema import CodeSnippet
from modules.integrations.tools.search.abc_search import Search<PERSON>oolAB<PERSON>
from utils.convert import safe_parse_xml_with_preprocessing, extract_text_from_xml_element
from utils.trace_logger import get_trace_logger
from utils.file import read_chunk_contents

logger = get_trace_logger(__name__)


class CompleteSearchTool(SearchToolABC):
    def __init__(self, repo_path: str, **args):
        self.repo_path = repo_path

    def search(self, query: str, **kwargs) -> List[CodeSnippet]:
        """
        处理搜索完成标识，返回指定文件的代码片段

        Args:
            query: 查询字符串，包含文件路径和评分信息
            **kwargs: 额外参数

        Returns:
            List[CodeSnippet]: 指定文件的代码片段列表
        """
        try:

            # 解析查询参数
            search_params = self._parse_query(query)

            file_paths = search_params['file_paths']
            scores = search_params['scores']

            snippets = []

            # 为每个文件创建代码片段
            for i, file_path in enumerate(file_paths):
                score = scores[i] if i < len(scores) else 1.0

                # 验证文件是否存在
                full_path = os.path.join(self.repo_path, file_path)
                if not os.path.exists(full_path):
                    logger.warning(f"File not found: {file_path}")
                    continue

                # 创建代码片段（表示整个文件）
                snippet = CodeSnippet(
                    file_path=file_path,
                    start_line=0,
                    end_line=-1,  # -1 表示整个文件
                    content="",   # 内容将在需要时读取
                    context_before="",
                    context_after="",
                    score=score
                )
                snippets.append(snippet)

            logger.info(f"Complete search returned {len(snippets)} file(s)")

        except Exception as e:
            logger.error(f"Complete search failed: {e}")
            return []

        # 提取 content 内容
        chunk_contents = read_chunk_contents(self.repo_path, [f"{snippet.file_path}:{snippet.start_line}-{snippet.end_line}" for snippet in snippets])

        for snippet_idx, snippet in enumerate(snippets):
            snippets[snippet_idx].content = chunk_contents[f"{snippet.file_path}:{snippet.start_line}-{snippet.end_line}"]

        return snippets

    def _parse_query(self, query: str) -> dict:
        """
        解析查询参数，支持XML格式和普通文本

        支持以下XML格式：
        1. 标准格式: <complete><file_paths>path1,path2</file_paths><scores>0.9,0.8</scores></complete>
        2. 简化格式: 直接提供逗号分隔的文件路径

        Args:
            query: 查询字符串

        Returns:
            dict: 解析后的参数字典
        """
        # 默认参数
        params = {
            'file_paths': [],
            'scores': []
        }

        # 尝试解析XML格式
        if query.strip().startswith('<complete>'):
            try:
                # 使用通用的XML预处理和解析函数
                root = safe_parse_xml_with_preprocessing(query.strip(), 'file_paths')

                if root is not None:
                    # 提取文件路径
                    file_paths_elem = root.find('file_paths')
                    if file_paths_elem is not None:
                        file_paths_content = extract_text_from_xml_element(file_paths_elem)
                        if file_paths_content:
                            # 分割文件路径并清理空白
                            file_paths = [path.strip() for path in file_paths_content.split(',') if path.strip()]
                            params['file_paths'] = file_paths

                    # 提取评分
                    scores_elem = root.find('scores')
                    if scores_elem is not None:
                        scores_content = extract_text_from_xml_element(scores_elem)
                        if scores_content:
                            try:
                                # 分割评分并转换为浮点数
                                scores = [float(score.strip()) for score in scores_content.split(',') if score.strip()]
                                params['scores'] = scores
                            except ValueError:
                                logger.warning(f"Invalid scores format: {scores_content}, using default scores")

            except Exception as e:
                logger.warning(f"Failed to parse XML query, using as plain text: {e}")

        # 如果XML解析失败或没有找到文件路径，尝试作为普通文本处理
        if not params['file_paths']:
            # 假设普通文本是逗号分隔的文件路径
            file_paths = [path.strip() for path in query.split(',') if path.strip()]
            params['file_paths'] = file_paths
            # 为每个文件设置默认评分
            params['scores'] = [1.0] * len(file_paths)

        return params

    @property
    def description(self):
        return """- `complete`: Use this tool when you have identified the relevant files that contain the information needed to answer the user's queries.

**Primary Use Case**:
- **Search Termination**: When the current context and identified files are sufficient to answer the user's queries
- **File Collection**: Collect and return specific files that are relevant to the query

**When to Use This Tool**:
- ✅ You have identified specific files that contain the answer to the user's query
- ✅ The current context provides enough information to determine relevant files
- ✅ You want to conclude the search process with a specific set of files
- ✅ You need to provide relevance scores for the identified files
- ❌ Don't use when you still need to search for more information

**Parameters**:
- file_paths: Comma-separated relative file paths from the repository root
- scores: (optional) Comma-separated relevance scores (0.0-1.0) for each file

**Important Notes**:
- File paths must be relative to the repository root
- File paths should be known/verified paths from previous search results or directory listings
- If scores are not provided, default score of 1.0 will be assigned to all files
- Files that don't exist will be skipped with a warning"""

    @property
    def examples(self):
        return """<output>
    <complete>
    <file_paths>src/auth/login.py,src/auth/models.py,src/utils/validators.py</file_paths>
    <scores>0.95,0.85,0.70</scores>
    </complete>

    <complete>
    <file_paths>backend/api/users.py,backend/models/user.py</file_paths>
    <scores>0.90,0.80</scores>
    </complete>

    <complete>
    <file_paths>config/database.py,src/db/connection.py,src/db/models.py</file_paths>
    </complete>

    <complete>
    <file_paths>README.md,docs/installation.md</file_paths>
    <scores>1.0,0.8</scores>
    </complete>
</output>"""

# 标识搜索可以结束的工具

# Usage:
# - 当已有的上下文信息对应的文件信息已经足够回答用户的问题时，调用本工具结束搜索

# - Parameters:
# - file_paths: 文件的相对路径，多个文件之间使用 , 进行分隔，文件路径必须是已知的文件路径，从目录上下文信息中得到的路径需要你拼接目录与目录下的文件路径
# - scores：文件的相关性评分，多个评分之间使用 , 进行分隔
