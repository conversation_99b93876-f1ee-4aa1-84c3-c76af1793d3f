from typing import List
from modules.common.schema import CodeSnippet
from modules.integrations.tools.search.abc_search import SearchToolABC


class CompleteSearchTool(SearchToolABC):
    def __init__(self, repo_path, **args):
        self.repo_path= repo_path
    
    def search(self, query: str, **kwargs) -> List[CodeSnippet]:
        pass
    
    def _parse_query(self, query: str) -> dict:
        pass

    @property
    def description(self):
        return """- `complete`: 

"""
    @property
    def examples(self):
        pass

# 标识搜索可以结束的工具

# Usage:
# - 当已有的上下文信息对应的文件信息已经足够回答用户的问题时，调用本工具结束搜索

# - Parameters:
# - file_paths: 文件的相对路径，多个文件之间使用 , 进行分隔，文件路径必须是已知的文件路径，从目录上下文信息中得到的路径需要你拼接目录与目录下的文件路径
# - scores：文件的相关性评分，多个评分之间使用 , 进行分隔
