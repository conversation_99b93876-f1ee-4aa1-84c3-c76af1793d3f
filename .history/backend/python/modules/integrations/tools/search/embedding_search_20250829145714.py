from typing import List

from modules.integrations.tools.search.any_search import CodeSnippet, SearchToolABC
from modules.integrations.apis.embedding.embedding_client import EmbeddingClient


class EmbeddingSearchTool(SearchToolABC):
    def __init__(self, repo_path: str, refresh: bool = True, ):
        self.repo_path = repo_path
        self.embedding_client = EmbeddingClient()

        # 如果refresh为True，则强制重新上传

        # 如果refresh为False，则判断当前repo是否已经上传到向量数据库，如果没有则进行上传
    
    def upload_project(self):
        pass
    
    def refresh_project(self):
        pass

    def search(self, query: str, **kwargs) -> List[CodeSnippet]:
        pass
        
