import os
import asyncio
import hashlib
from typing import List, Optional

from modules.integrations.tools.search.abc_search import SearchToolABC
from modules.common.schema import CodeSnippet
from modules.integrations.apis.embedding.embedding_client import EmbeddingClient
from modules.integrations.apis.embedding.schemas import (
    ProjectCreateRequest,
    ProjectCreateResponse,
    CompareRequest,
    UploadRequest,
    QueryRequest,
    MerkleNode,
    FileUploadItem,
    VectorSearchResult,
)
from utils.file import build_file_tree, build_file_list
from modules.common.constants import FileFilterMode
from utils.trace_logger import get_trace_logger

logger = get_trace_logger(__name__)


class EmbeddingSearchTool(SearchToolABC):
    def __init__(self, repo_path: str, refresh: bool = True):
        self.repo_path = repo_path
        self.embedding_client = EmbeddingClient()
        self.project_id: Optional[str] = None

        """初始化项目，创建或获取项目ID"""
        try:
            # 创建或获取项目
            project_response = await self._create_or_get_project()
            self.project_id = project_response.project_id

            # 如果 refresh 或者 is_new 开始比较项目并且上传项目，阻塞等待项目完成
            if refresh or project_response.is_new:
                await self._upload_and_wait_completion()

        except Exception as e:
            logger.error(f"Failed to initialize project: {e}")
            raise

    async def _create_or_get_project(self) -> ProjectCreateResponse:
        """创建或获取项目"""
        # 生成项目请求
        request = ProjectCreateRequest(
            username="codebase_url",  # 可以从配置中获取
            source="local",
            branch="main",  # 可以从git信息中获取
            repo_url=f"file://{self.repo_path}"
        )

        return await self.embedding_client.create_project(request)

    async def _upload_and_wait_completion(self):
        """上传项目文件并等待完成"""
        try:
            # 构建Merkle树
            merkle_tree = self._build_merkle_tree()

            # 比较与服务器的差异
            compare_request = CompareRequest(merkle_tree=merkle_tree)
            compare_response = await self.embedding_client.compare_with_server(
                self.project_id, compare_request
            )

            # 如果有差异文件，上传它们
            diff_files = compare_response.diff_files
            if diff_files.added or diff_files.modified:
                await self._upload_files(compare_response.task_id, diff_files)
                await self._wait_for_completion(compare_response.task_id)

        except Exception as e:
            logger.error(f"Failed to upload and process project: {e}")
            raise

    def _build_merkle_tree(self) -> MerkleNode:
        """构建项目的Merkle树"""
        def build_node(file_node) -> MerkleNode:
            if file_node.type == "file":
                # 计算文件哈希
                file_hash = self._calculate_file_hash(file_node.path)
                return MerkleNode(
                    path=file_node.name,
                    hash=file_hash,
                    is_file=True,
                    children=[]
                )
            else:
                # 目录节点
                children = [build_node(child) for child in file_node.children]
                # 目录哈希基于子节点哈希
                dir_hash = self._calculate_directory_hash(children)
                return MerkleNode(
                    path=file_node.name,
                    hash=dir_hash,
                    is_file=False,
                    children=children
                )

        # 获取文件树
        file_nodes = build_file_tree(
            root_dir=self.repo_path,
            start_dir=self.repo_path,
            filter_mode=FileFilterMode.LOCAL
        )

        # 构建根节点
        if len(file_nodes) == 1:
            return build_node(file_nodes[0])
        else:
            # 多个根节点，创建虚拟根节点
            children = [build_node(node) for node in file_nodes]
            root_hash = self._calculate_directory_hash(children)
            return MerkleNode(
                path=".",
                hash=root_hash,
                is_file=False,
                children=children
            )

    def _calculate_file_hash(self, file_path: str) -> str:
        """计算文件内容的哈希值"""
        try:
            full_path = os.path.join(self.repo_path, file_path)
            with open(full_path, 'rb') as f:
                content = f.read()
                return hashlib.sha256(content).hexdigest()
        except Exception as e:
            logger.warning(f"Failed to calculate hash for {file_path}: {e}")
            # 返回基于文件路径的哈希作为fallback
            return hashlib.sha256(file_path.encode()).hexdigest()

    def _calculate_directory_hash(self, children: List[MerkleNode]) -> str:
        """计算目录哈希（基于子节点哈希）"""
        if not children:
            return hashlib.sha256(b"").hexdigest()

        # 按路径排序确保一致性
        sorted_children = sorted(children, key=lambda x: x.path)
        combined_hash = "".join([child.hash for child in sorted_children])
        return hashlib.sha256(combined_hash.encode()).hexdigest()

    async def _upload_files(self, task_id: str, diff_files):
        """上传差异文件"""
        files_to_upload = []

        # 读取需要上传的文件内容
        all_files = diff_files.added + diff_files.modified
        file_contents = build_file_list(
            root_dir=self.repo_path,
            start_dir=self.repo_path,
            filter_mode=FileFilterMode.LOCAL
        )

        for file_path in all_files:
            if file_path in file_contents:
                file_hash = self._calculate_file_hash(file_path)
                files_to_upload.append(FileUploadItem(
                    file_path=file_path,
                    file_content=file_contents[file_path],
                    file_hash=file_hash
                ))

        if files_to_upload:
            upload_request = UploadRequest(
                files=files_to_upload,
                auto_commit=True
            )
            await self.embedding_client.upload_files(task_id, upload_request)

    async def _wait_for_completion(self, task_id: str, max_wait_time: int = 300):
        """等待任务完成"""
        import time
        start_time = time.time()

        while time.time() - start_time < max_wait_time:
            try:
                status_response = await self.embedding_client.get_task_status(task_id)

                if status_response.is_terminal:
                    if status_response.status == "success":
                        logger.info(f"Task {task_id} completed successfully")
                        return
                    else:
                        error_msg = status_response.error_message or "Unknown error"
                        raise Exception(f"Task {task_id} failed: {error_msg}")

                # 等待一段时间后重试
                await asyncio.sleep(5)

            except Exception as e:
                logger.error(f"Error checking task status: {e}")
                await asyncio.sleep(5)

        raise TimeoutError(f"Task {task_id} did not complete within {max_wait_time} seconds")

    def search(self, query: str, **kwargs) -> List[CodeSnippet]:
        """同步搜索接口"""
        return asyncio.run(self.search_async(query, **kwargs))

    async def search_async(self, query: str, **kwargs) -> List[CodeSnippet]:
        """异步搜索代码库"""
        if not self.project_id:
            raise ValueError("Project not initialized")

        try:
            # 构建查询请求
            top_k = kwargs.get('top_k', 10)
            score_threshold = kwargs.get('score_threshold', 0.0)
            retrieval_type = kwargs.get('retrieval_type', None)

            query_request = QueryRequest(
                query=query,
                project_id=self.project_id,
                retrieval_type=retrieval_type,
                top_k=top_k,
                score_threshold=score_threshold,
                rewrite=kwargs.get('rewrite', False)
            )

            # 执行查询
            query_response = await self.embedding_client.query_codebase(query_request)

            # 转换结果为CodeSnippet
            return self._convert_to_code_snippets(query_response.documents)

        except Exception as e:
            logger.error(f"Search failed: {e}")
            return []

    def _convert_to_code_snippets(self, documents: List[VectorSearchResult]) -> List[CodeSnippet]:
        """将向量搜索结果转换为CodeSnippet"""
        snippets = []

        for doc in documents:
            try:
                metadata = doc.metadata

                # 解析位置信息
                start_line = metadata.location[0] if metadata.location else 0
                end_line = metadata.location[1] if len(metadata.location) > 1 else start_line

                # 读取文件内容
                content = self._read_file_content(metadata.file_path, start_line, end_line)

                snippet = CodeSnippet(
                    file_path=metadata.file_path,
                    start_line=start_line,
                    end_line=end_line,
                    content=content,
                    context_before="",  # 可以根据需要添加上下文
                    context_after="",   # 可以根据需要添加上下文
                    score=doc.score
                )
                snippets.append(snippet)

            except Exception as e:
                logger.warning(f"Failed to convert document to snippet: {e}")
                continue

        return snippets

    def _read_file_content(self, file_path: str, start_line: int, end_line: int) -> str:
        """读取文件指定行范围的内容"""
        try:
            full_path = os.path.join(self.repo_path, file_path)
            with open(full_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()

                # 确保行号在有效范围内
                start_idx = max(0, start_line)
                end_idx = min(len(lines), end_line + 1)

                return ''.join(lines[start_idx:end_idx])

        except Exception as e:
            logger.warning(f"Failed to read file content {file_path}:{start_line}-{end_line}: {e}")
            return ""

    def upload_project(self):
        """上传项目（同步接口）"""
        asyncio.run(self._upload_and_wait_completion())

    def refresh_project(self):
        """刷新项目（重新上传）"""
        asyncio.run(self._upload_and_wait_completion())

    @property
    def description(self) -> str:
        """搜索工具描述"""
        return """
Embedding Search Tool - 基于向量嵌入的语义搜索

这个工具使用深度学习模型将代码转换为向量表示，支持语义相似性搜索。
特别适合以下场景：
- 概念性搜索：查找实现特定功能的代码
- 跨语言搜索：查找不同编程语言中的相似实现
- 模糊搜索：当不确定确切关键词时的搜索
- 代码理解：查找与给定代码片段功能相似的代码

参数说明：
- top_k: 返回结果数量（默认10）
- score_threshold: 相似度阈值（默认0.0）
- retrieval_type: 检索类型（可选）
- rewrite: 是否重写查询（默认False）
        """.strip()

    @property
    def examples(self) -> str:
        """搜索示例"""
        return """
示例查询：

1. 功能性搜索：
   - "database connection"
   - "file upload handling"
   - "user authentication"
   - "error handling"

2. 概念性搜索：
   - "implement caching mechanism"
   - "parse JSON data"
   - "validate user input"
   - "generate random numbers"

3. 算法搜索：
   - "sorting algorithm"
   - "binary search implementation"
   - "recursive function"
   - "data structure operations"

4. 架构模式搜索：
   - "singleton pattern"
   - "factory method"
   - "observer pattern"
   - "dependency injection"
        """.strip()
        
